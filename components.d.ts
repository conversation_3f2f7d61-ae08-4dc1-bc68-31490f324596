/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ClinicTabPanel: typeof import('./src/components/ClinicTabPanel.vue')['default']
    Combobox: typeof import('@headlessui/vue')['Combobox']
    ComboboxButton: typeof import('@headlessui/vue')['ComboboxButton']
    ComboboxInput: typeof import('@headlessui/vue')['ComboboxInput']
    ComboboxLabel: typeof import('@headlessui/vue')['ComboboxLabel']
    ComboboxOption: typeof import('@headlessui/vue')['ComboboxOption']
    ComboboxOptions: typeof import('@headlessui/vue')['ComboboxOptions']
    CompanyEmptyState: typeof import('./src/components/CompanyEmptyState.vue')['default']
    Dialog: typeof import('@headlessui/vue')['Dialog']
    DialogOverlay: typeof import('@headlessui/vue')['DialogOverlay']
    DialogPanel: typeof import('@headlessui/vue')['DialogPanel']
    DrugScreeningForm: typeof import('./src/components/DrugScreeningForm.vue')['default']
    EditButton: typeof import('./src/components/EditButton.vue')['default']
    EmploymentAddForm: typeof import('./src/components/EmploymentAddForm.vue')['default']
    EmptyMedicalState: typeof import('./src/components/EmptyMedicalState.vue')['default']
    EmptyPatientState: typeof import('./src/components/EmptyPatientState.vue')['default']
    EmptyState: typeof import('./src/components/EmptyState.vue')['default']
    ErrorState: typeof import('./src/components/ErrorState.vue')['default']
    EssentialLink: typeof import('./src/components/EssentialLink.vue')['default']
    Listbox: typeof import('@headlessui/vue')['Listbox']
    ListboxButton: typeof import('@headlessui/vue')['ListboxButton']
    ListboxLabel: typeof import('@headlessui/vue')['ListboxLabel']
    ListboxOption: typeof import('@headlessui/vue')['ListboxOption']
    ListboxOptions: typeof import('@headlessui/vue')['ListboxOptions']
    LoadingOverlay: typeof import('./src/components/LoadingOverlay.vue')['default']
    MedicalForm: typeof import('./src/components/MedicalForm.vue')['default']
    NarrowSidebar: typeof import('./src/components/NarrowSidebar.vue')['default']
    PageHeading: typeof import('./src/components/PageHeading.vue')['default']
    PatientEditForm: typeof import('./src/components/PatientEditForm.vue')['default']
    PatientForm: typeof import('./src/components/PatientForm.vue')['default']
    PatientShowForm: typeof import('./src/components/PatientShowForm.vue')['default']
    QuasarButton: typeof import('./src/components/QuasarButton.vue')['default']
    QuasarDialog: typeof import('./src/components/QuasarDialog.vue')['default']
    QuasarDrawer: typeof import('./src/components/QuasarDrawer.vue')['default']
    QuasarPageSticky: typeof import('./src/components/QuasarPageSticky.vue')['default']
    QuasarTooltip: typeof import('./src/components/QuasarTooltip.vue')['default']
    RadioGroup: typeof import('@headlessui/vue')['RadioGroup']
    RadioGroupLabel: typeof import('@headlessui/vue')['RadioGroupLabel']
    RadioGroupOption: typeof import('@headlessui/vue')['RadioGroupOption']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShowText: typeof import('./src/components/ShowText.vue')['default']
    TableList: typeof import('./src/components/TableList.vue')['default']
    TransitionChild: typeof import('@headlessui/vue')['TransitionChild']
    TransitionRoot: typeof import('@headlessui/vue')['TransitionRoot']
  }
}
