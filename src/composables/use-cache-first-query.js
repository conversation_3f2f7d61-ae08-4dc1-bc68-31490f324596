import { useQuery } from '@vue/apollo-composable'
import { computed, ref, watchEffect, onMounted, inject } from 'vue'
import { DefaultApolloClient } from '@vue/apollo-composable'

export function useCacheFirstQuery(query, variables, options = {}) {
  const hasCachedData = ref(false)
  const cacheError = ref(null)

  // Start with cache-first to show cached data immediately
  const queryOptions = {
    ...options,
    fetchPolicy: 'cache-first',
    nextFetchPolicy: 'cache-and-network', // Background refresh after cache
    notifyOnNetworkStatusChange: true,
    errorPolicy: 'all', // Continue showing cached data even if network fails
  }

  const { result, loading, error, refetch, networkStatus } = useQuery(
    query,
    variables,
    queryOptions
  )

  // Get Apollo client for cache operations
  const apolloClient = inject(DefaultApolloClient)

  // Handle cache read failures with automatic network fallback
  onMounted(async () => {
    try {
      // Attempt to read from cache
      const cachedData = apolloClient.readQuery({
        query,
        variables,
      })

      if (cachedData) {
        hasCachedData.value = true
        console.log('[Cache] Found cached data for query')
      }
    } catch (err) {
      console.warn('[Cache] Failed to read from cache, falling back to network:', err)
      cacheError.value = err

      // Force network request if cache read fails
      try {
        await refetch()
      } catch (refetchError) {
        console.error('[Cache] Network fallback also failed:', refetchError)
      }
    }
  })

  // Track if we have cached data
  watchEffect(() => {
    if (result.value && !loading.value) {
      hasCachedData.value = true
      cacheError.value = null // Clear any cache errors once we have data
    }
  })

  // Compute loading states
  const isInitialLoad = computed(() => loading.value && !hasCachedData.value)
  const isRefreshing = computed(() => loading.value && hasCachedData.value)
  const isCacheError = computed(() => !!cacheError.value && !result.value)

  // Log cache status in development
  if (process.env.DEV) {
    watchEffect(() => {
      console.log('[Cache Query Status]', {
        hasCachedData: hasCachedData.value,
        isInitialLoad: isInitialLoad.value,
        isRefreshing: isRefreshing.value,
        networkStatus: networkStatus.value,
        hasError: !!error.value,
        cacheError: cacheError.value,
      })
    })
  }

  return {
    result,
    loading: isInitialLoad,
    isRefreshing,
    isCacheError,
    error: computed(() => error.value || cacheError.value),
    refetch,
    hasCachedData,
  }
}
