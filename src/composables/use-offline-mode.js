import { ref, computed, watch } from 'vue'

// Global offline state - shared across the entire app
const isOfflineMode = ref(false)
const offlineStartTime = ref(null)

/**
 * Global offline mode composable for testing network connectivity
 * This simulates offline behavior for testing cache-first strategies
 */
export function useOfflineMode() {
  // Toggle offline mode
  const toggleOfflineMode = () => {
    isOfflineMode.value = !isOfflineMode.value
    
    if (isOfflineMode.value) {
      offlineStartTime.value = new Date()
      console.log('[Offline Mode] Simulated offline mode activated')
      
      // Store offline state in localStorage for persistence
      localStorage.setItem('dev-offline-mode', 'true')
      localStorage.setItem('dev-offline-start-time', offlineStartTime.value.toISOString())
      
      // Dispatch custom event for other components to listen
      window.dispatchEvent(new CustomEvent('offline-mode-changed', { 
        detail: { isOffline: true, startTime: offlineStartTime.value }
      }))
    } else {
      const duration = offlineStartTime.value ? 
        Math.round((new Date() - offlineStartTime.value) / 1000) : 0
      
      console.log(`[Offline Mode] Simulated offline mode deactivated (was offline for ${duration}s)`)
      
      offlineStartTime.value = null
      
      // Remove offline state from localStorage
      localStorage.removeItem('dev-offline-mode')
      localStorage.removeItem('dev-offline-start-time')
      
      // Dispatch custom event
      window.dispatchEvent(new CustomEvent('offline-mode-changed', { 
        detail: { isOffline: false, duration }
      }))
    }
  }

  // Set offline mode (for programmatic control)
  const setOfflineMode = (offline) => {
    if (offline !== isOfflineMode.value) {
      toggleOfflineMode()
    }
  }

  // Get offline duration
  const offlineDuration = computed(() => {
    if (!isOfflineMode.value || !offlineStartTime.value) return 0
    return Math.round((new Date() - offlineStartTime.value) / 1000)
  })

  // Format offline duration for display
  const formattedOfflineDuration = computed(() => {
    const seconds = offlineDuration.value
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  })

  // Restore offline state from localStorage on app start
  const restoreOfflineState = () => {
    const storedOfflineMode = localStorage.getItem('dev-offline-mode')
    const storedStartTime = localStorage.getItem('dev-offline-start-time')
    
    if (storedOfflineMode === 'true') {
      isOfflineMode.value = true
      offlineStartTime.value = storedStartTime ? new Date(storedStartTime) : new Date()
      
      console.log('[Offline Mode] Restored offline mode from previous session')
      
      // Dispatch event for components that might have missed the initial state
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('offline-mode-changed', { 
          detail: { isOffline: true, startTime: offlineStartTime.value, restored: true }
        }))
      }, 100)
    }
  }

  // Network request interceptor for simulating offline behavior
  const shouldBlockRequest = (url) => {
    if (!isOfflineMode.value) return false
    
    // Allow requests to localhost/dev server
    if (url.includes('localhost') || url.includes('127.0.0.1')) {
      return false
    }
    
    // Block GraphQL requests when offline
    if (url.includes('/graphql') || url.includes('graphql')) {
      console.log('[Offline Mode] Blocking GraphQL request:', url)
      return true
    }
    
    // Block other API requests
    if (url.includes('/api/')) {
      console.log('[Offline Mode] Blocking API request:', url)
      return true
    }
    
    return false
  }

  // Create a network error for blocked requests
  const createNetworkError = (url) => {
    const error = new Error(`Network request failed: ${url}`)
    error.name = 'NetworkError'
    error.code = 'NETWORK_ERROR'
    error.offline = true
    return error
  }

  // Apollo Link error handler for offline mode
  const handleApolloRequest = (operation, forward) => {
    if (isOfflineMode.value) {
      console.log('[Offline Mode] Apollo request blocked:', operation.operationName)
      
      // Return an error that Apollo can handle
      return new Promise((resolve, reject) => {
        reject(createNetworkError(`GraphQL operation: ${operation.operationName}`))
      })
    }
    
    return forward(operation)
  }

  return {
    // State
    isOfflineMode: computed(() => isOfflineMode.value),
    offlineStartTime: computed(() => offlineStartTime.value),
    offlineDuration,
    formattedOfflineDuration,
    
    // Actions
    toggleOfflineMode,
    setOfflineMode,
    restoreOfflineState,
    
    // Utilities
    shouldBlockRequest,
    createNetworkError,
    handleApolloRequest,
  }
}

// Auto-restore offline state when the module is imported
if (typeof window !== 'undefined') {
  const { restoreOfflineState } = useOfflineMode()
  restoreOfflineState()
}
