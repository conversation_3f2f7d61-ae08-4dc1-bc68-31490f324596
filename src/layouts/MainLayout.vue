<template>
  <q-layout>
    <q-page-container>
      <q-page class="flex h-full flex-nowrap">
        <NarrowSidebar />

        <TransitionRoot as="template" :show="mobileMenuOpen">
          <Dialog
            as="div"
            class="md:hidden"
            @close="mobileMenuOpen = false"
          >
            <div class="fixed inset-0 z-40 flex">
              <TransitionChild
                as="template"
                enter="transition-opacity ease-linear duration-300"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="transition-opacity ease-linear duration-300"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <DialogOverlay
                  class="fixed inset-0 bg-gray-600 bg-opacity-75"
                />
              </TransitionChild>
              <TransitionChild
                as="template"
                enter="transition ease-in-out duration-300 transform"
                enter-from="-translate-x-full"
                enter-to="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leave-from="translate-x-0"
                leave-to="-translate-x-full"
              >
                <div
                  class="relative flex flex-col flex-1 w-full max-w-xs pt-5 pb-4 bg-teal-700"
                >
                  <TransitionChild
                    as="template"
                    enter="ease-in-out duration-300"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="ease-in-out duration-300"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                  >
                    <div class="absolute right-0 p-1 top-1 -mr-14">
                      <button
                        type="button"
                        class="flex items-center justify-center w-12 h-12 rounded-full focus:outline-none focus:ring-2 focus:ring-white"
                        @click="mobileMenuOpen = false"
                      >
                        <XMarkIcon
                          class="w-6 h-6 text-white"
                          aria-hidden="true"
                        />
                        <span class="sr-only">Close sidebar</span>
                      </button>
                    </div>
                  </TransitionChild>
                  <div class="flex items-center flex-shrink-0 px-4">
                    <img
                      class="w-auto h-8"
                      src="/logo.png"
                      alt="Workflow"
                    />
                  </div>
                  <div class="flex-1 h-0 px-2 mt-5 overflow-y-auto">
                    <nav class="flex flex-col h-full">
                      <div class="space-y-1">
                        <router-link
                          to="/"
                          custom
                          v-slot="{ href, navigate, isExactActive }"
                        >
                          <a
                            :href="href"
                            @click="navigate"
                            :class="[
                              isExactActive
                                ? 'bg-teal-800 text-white'
                                : 'text-teal-100 hover:bg-teal-800 hover:text-white',
                              'group py-2 px-3 rounded-md flex items-center text-sm font-medium',
                            ]"
                          >
                            <HomeIcon
                              :class="[
                                isExactActive
                                  ? 'text-white'
                                  : 'text-teal-300 group-hover:text-white',
                                'mr-3 h-6 w-6',
                              ]"
                              aria-hidden="false"
                            />
                            <span>Home</span>
                          </a>
                        </router-link>

                        <router-link
                          to="/patients"
                          custom
                          v-slot="{ href, navigate, isExactActive }"
                        >
                          <a
                            :href="href"
                            @click="navigate"
                            :class="[
                              isExactActive
                                ? 'bg-teal-800 text-white'
                                : 'text-teal-100 hover:bg-teal-800 hover:text-white',
                              'group py-2 px-3 rounded-md flex items-center text-sm font-medium',
                            ]"
                          >
                            <Squares2X2Icon
                              :class="[
                                isExactActive
                                  ? 'text-white'
                                  : 'text-teal-300 group-hover:text-white',
                                'mr-3 h-6 w-6',
                              ]"
                              aria-hidden="false"
                            />
                            <span>Patients</span>
                          </a>
                        </router-link>

                        <router-link
                          to="/companies"
                          custom
                          v-slot="{ href, navigate, isExactActive }"
                        >
                          <a
                            :href="href"
                            @click="navigate"
                            :class="[
                              isExactActive
                                ? 'bg-teal-800 text-white'
                                : 'text-teal-100 hover:bg-teal-800 hover:text-white',
                              'group py-2 px-3 rounded-md flex items-center text-sm font-medium',
                            ]"
                          >
                            <CubeIcon
                              :class="[
                                isExactActive
                                  ? 'text-white'
                                  : 'text-teal-300 group-hover:text-white',
                                'mr-3 h-6 w-6',
                              ]"
                              aria-hidden="false"
                            />
                            <span>Companies</span>
                          </a>
                        </router-link>

                        <router-link
                          to="/medicals"
                          custom
                          v-slot="{ href, navigate, isExactActive }"
                        >
                          <a
                            :href="href"
                            @click="navigate"
                            :class="[
                              isExactActive
                                ? 'bg-teal-800 text-white'
                                : 'text-teal-100 hover:bg-teal-800 hover:text-white',
                              'group py-2 px-3 rounded-md flex items-center text-sm font-medium',
                            ]"
                          >
                            <RectangleStackIcon
                              :class="[
                                isExactActive
                                  ? 'text-white'
                                  : 'text-teal-300 group-hover:text-white',
                                'mr-3 h-6 w-6',
                              ]"
                              aria-hidden="false"
                            />
                            <span>Medicals</span>
                          </a>
                        </router-link>

                        <router-link
                          to="/settings"
                          custom
                          v-slot="{ href, navigate, isExactActive }"
                        >
                          <a
                            :href="href"
                            @click="navigate"
                            :class="[
                              isExactActive
                                ? 'bg-teal-800 text-white'
                                : 'text-teal-100 hover:bg-teal-800 hover:text-white',
                              'group py-2 px-3 rounded-md flex items-center text-sm font-medium',
                            ]"
                          >
                            <CogIcon
                              :class="[
                                isExactActive
                                  ? 'text-white'
                                  : 'text-teal-300 group-hover:text-white',
                                'mr-3 h-6 w-6',
                              ]"
                              aria-hidden="false"
                            />
                            <span>Settings</span>
                          </a>
                        </router-link>
                      </div>
                    </nav>
                  </div>
                </div>
              </TransitionChild>
              <div class="flex-shrink-0 w-14" aria-hidden="true">
                <!-- Dummy element to force sidebar to shrink to fit close icon -->
              </div>
            </div>
          </Dialog>
        </TransitionRoot>
        <!-- Content area -->
        <div class="flex flex-col flex-1">
          <header class="w-full">
            <div
              class="relative z-10 flex flex-shrink-0 h-16 bg-white border-b border-gray-200 shadow-sm"
            >
              <button
                type="button"
                class="px-4 text-gray-500 border-r border-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-teal-500 md:hidden"
                @click="mobileMenuOpen = true"
              >
                <span class="sr-only">Open sidebar</span>
                <Bars3BottomLeftIcon
                  class="w-6 h-6"
                  aria-hidden="true"
                />
              </button>
              <div class="flex justify-between flex-1 px-4 sm:px-6">
                <!-- <div class="flex flex-1">
                  <form
                    class="flex w-full md:ml-0"
                    action="#"
                    method="GET"
                  >
                    <label for="search-field" class="sr-only"
                      >Search all files</label
                    >
                    <div
                      class="relative w-full text-gray-400 focus-within:text-gray-600"
                    > -->
                <!-- <div
                        class="absolute inset-y-0 left-0 flex items-center pointer-events-none"
                      >
                        <MagnifyingGlassIcon
                          class="flex-shrink-0 w-5 h-5"
                          aria-hidden="true"
                        />
                      </div>
                      <input
                        id="search-field"
                        name="search-field"
                        class="w-full h-full py-2 pl-8 pr-3 text-base text-gray-900 placeholder-gray-500 border-transparent focus:outline-none focus:ring-0 focus:border-transparent focus:placeholder-gray-400"
                        placeholder="Search"
                        type="search"
                      /> -->
                <!-- </div>
                  </form> -->
                <!-- </div> -->
              </div>
            </div>
          </header>

          <!-- Main content -->
          <div class="flex items-stretch flex-1">
            <main class="flex-1">
              <!-- Primary column -->
              <section
                aria-labelledby="primary-heading"
                class="flex flex-col flex-1 h-full min-w-0 lg:order-last"
              >
                <!-- Your content -->
                <router-view />
              </section>
            </main>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from "vue";

import {
  CogIcon,
  Bars3BottomLeftIcon,
  RectangleStackIcon,
  HomeIcon,
  Squares2X2Icon,
  XMarkIcon,
  CubeIcon,
} from "@heroicons/vue/24/outline";

const mobileMenuOpen = ref(false);
</script>
