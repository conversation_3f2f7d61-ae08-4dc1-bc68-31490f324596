<template>
  <q-page padding>
    <div class="text-xl text-stone-500 font-medium">
      Welcome, Team MarianMed
    </div>

    <div class="text-4xl mt-3 font-bold">
      Things are looking good today.
    </div>

    <div class="mt-8 flex items-center">
      <router-link to="/patients/new">
        <span class="flex items-center">
          <div class="p-0.5 bg-stone-200 rounded">
            <PlusSmallIcon class="text-black w-4 h-4" />
          </div>
          <div class="ml-3 text-md text-stone-500 text-bold">
            Add Patient
          </div>
        </span>
      </router-link>

      <div class="lg:ml-8 ml-4">
        <router-link to="/companies/new" class="">
          <div class="flex items-center">
            <div class="p-0.5 bg-stone-200 rounded">
              <PlusSmallIcon class="text-black w-4 h-4" />
            </div>
            <div class="ml-3 text-md text-stone-500 text-bold">
              Add Company
            </div>
          </div>
        </router-link>
      </div>
    </div>

    <div class="max-w-4xl">
      <div class="mt-10">
        <div class="" v-if="patientCount > 0">
          <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
              <h1 class="text-xl font-semibold text-gray-900">
                Recent Patients
              </h1>
              <p class="mt-2 text-sm text-gray-700">
                A table of employees we have on our records, modified
                or created in the last 3 weeks
              </p>
            </div>
          </div>
          <div class="flex flex-col mt-8">
            <div
              class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
            >
              <div
                class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
              >
                <div
                  class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                >
                  <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-stone-50">
                      <tr>
                        <th
                          scope="col"
                          class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                        >
                          Full Name
                        </th>
                        <th
                          scope="col"
                          class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Identification Number
                        </th>
                        <th
                          scope="col"
                          class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Company
                        </th>
                        <th
                          scope="col"
                          class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Phone Number
                        </th>
                        <th
                          scope="col"
                          class="relative whitespace-nowrap py-3.5 pl-3 pr-4 sm:pr-6"
                        >
                          <span class="sr-only">Show</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr
                        v-for="employees in patients"
                        :key="employees.id"
                      >
                        <td
                          class="py-2 pl-4 pr-3 text-sm text-gray-500 capitalize whitespace-nowrap sm:pl-6"
                        >
                          {{ employees.fullName }}
                        </td>
                        <td
                          class="px-2 py-2 text-sm font-medium text-gray-900 whitespace-nowrap"
                        >
                          {{ employees.identificationNumber }}
                        </td>
                        <td
                          class="px-2 py-2 text-sm text-gray-900 whitespace-nowrap"
                        >
                          {{ employees.employers?.slice(-1)[0].name }}
                        </td>
                        <td
                          class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"
                        >
                          {{ employees.phoneNumber }}
                        </td>

                        <td
                          class="relative py-2 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6"
                        >
                          <router-link
                            class="flex items-center font-bold text-teal-700 hover:text-teal-900"
                            :to="{
                              name: 'show_patient',
                              params: { id: employees?.id ?? 1 },
                            }"
                          >
                            Show<span class="sr-only"
                              >, {{ employees?.id ?? 1 }}</span
                            >
                            <ArrowSmallRightIcon
                              class="w-4 h-4 ml-1"
                            />
                          </router-link>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="" v-else>
          <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
              <h1 class="text-xl font-semibold text-gray-900">
                Recent Patients
              </h1>
              <p class="mt-2 text-sm text-gray-700">
                A table of patients we have on our records, that have
                been modified or created in the last 3 weeks
              </p>
            </div>
          </div>
          <div class="flex flex-col mt-8">
            <div
              class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
            >
              <div
                class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
              >
                <div
                  class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                >
                  <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-stone-50">
                      <tr>
                        <th
                          scope="col"
                          class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                        ></th>
                      </tr>
                    </thead>
                    <tbody
                      class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                    >
                      <tr class="">
                        <EmptyPatientState
                          class="flex justify-center p-4"
                        />
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { PlusSmallIcon } from "@heroicons/vue/24/outline";

import { useQuery } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed, onMounted } from "vue";
import { ArrowSmallRightIcon } from "@heroicons/vue/24/outline";
import EmptyPatientState from "src/components/EmptyPatientState.vue";

const PATIENTS = gql`
  query listPatients($id: ID!) {
    recentPatients(organisationId: $id) {
      id
      fullName
      phoneNumber
      identificationNumber
      employers {
        name
      }
    }
  }
`;

const { result } = useQuery(
  PATIENTS,
  { id: 1 },
  { fetchPolicy: "cache-only" },
);

const fetchFromNetwork = async () => {
  const { NetResult } = useQuery(
    PATIENTS,
    { id: 1 },
    {
      fetchPolicy: "network-only",
    },
  );
  await NetResult.value;
};

onMounted(fetchFromNetwork);

const patientCount = computed(() => {
  return patients.value?.length ?? 0;
});

const patients = computed(() => {
  return result.value?.recentPatients ?? "Patient";
});
</script>
