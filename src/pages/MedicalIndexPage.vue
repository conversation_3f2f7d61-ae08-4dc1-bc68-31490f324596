<template>
  <q-page padding>
    <div class="flex content-center justify-center h-full">
      <!-- Loading overlay for initial load -->
      <LoadingOverlay
        v-if="loading"
        message="Loading medicals..."
        size="medium"
        variant="primary"
      />

      <!-- Error state -->
      <ErrorState
        v-else-if="error && !medicals.length"
        title="Error loading medicals"
        :message="error.message"
        :details="error.stack || error.toString()"
        type="error"
        :show-retry="true"
        :show-refresh="true"
        :retrying="loading"
        @retry="refetch"
        @refresh="() => window.location.reload()"
      />

      <!-- Background refresh indicator -->
      <transition name="fade">
        <div
          v-if="isRefreshing"
          class="fixed top-4 right-4 z-50 bg-white shadow-lg rounded-lg p-3 border"
        >
          <div class="flex items-center space-x-2">
            <ArrowPathIcon
              class="w-4 h-4 text-blue-600 animate-spin"
            />
            <span class="text-sm text-blue-600">Updating...</span>
          </div>
        </div>
      </transition>

      <!-- Error banner if network refresh failed but we have cached data -->
      <div
        v-if="error && medicals.length"
        class="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md"
      >
        <div class="flex">
          <svg
            class="h-5 w-5 text-amber-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <div class="ml-3">
            <p class="text-sm text-amber-800">
              Unable to refresh data. Showing cached results.
            </p>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div
        v-else-if="!loading && medicals.length === 0"
        class="w-full"
      >
        <EmptyMedicalState />
      </div>

      <!-- Main content -->
      <div
        v-else-if="!loading && medicals.length > 0"
        class="w-full h-full"
      >
        <PageHeading class="pb-6">
          <template #heading>
            <div class="flex items-center">
              <span> Medicals </span>
              <span
                class="ml-2 bg-stone-700 px-2 py-1 rounded-md text-white uppercase text-sm"
                >All
              </span>
              <router-link
                class="ml-2 bg-stone-500 px-2 py-1 rounded-md text-white uppercase text-sm"
                to="draft_medicals"
                >draft
              </router-link>
              <router-link
                class="ml-2 bg-stone-500 px-2 py-1 rounded-md text-white uppercase text-sm"
                to="signed_off_medicals"
                >Signed Off
              </router-link>
            </div>
          </template>

          <template #description>
            {{ medicalCount }} medical records
          </template>
          <template #buttons>
            <q-btn
              flat
              class="text-white bg-teal-700 text-bold"
              padding="md"
              no-caps
              to="/medicals/new"
              type="submit"
            >
              <template v-slot:loading>
                <q-spinner-ios />
              </template>
              <PlusIcon class="w-5 h-5 mr-1 text-bold" />
              <div class="text-sm">Add Medical</div>
            </q-btn>
          </template>
        </PageHeading>

        <div class="px-4 sm:px-6 lg:px-8">
          <div class="flex flex-col mt-8">
            <div
              class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
            >
              <div
                class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
              >
                <div class="flex items-center my-4">
                  <input
                    type="text"
                    class="px-2 py-2 border border-gray-400 rounded"
                    placeholder="Search"
                    v-model="filter"
                  />

                  <div class="flex items-center ml-6 gap-2">
                    <input
                      type="checkbox"
                      id="ageFilter"
                      v-model="showOver55"
                      @change="toggleAgeFilter"
                    />
                    <label for="ageFilter">Show 55+ only</label>
                  </div>
                </div>

                <div
                  class="overflow-hidden shadow ring-0.5 ring-stone-50 ring-opacity-5 md:rounded-sm"
                >
                  <table class="min-w-full divide-y divide-gray-100">
                    <thead>
                      <tr
                        v-for="headerGroup in table.getHeaderGroups()"
                        :key="headerGroup.id"
                      >
                        <th
                          v-for="header in headerGroup.headers"
                          :key="header.id"
                          scope="col"
                          class="py-3 pl-4 pr-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase sm:pl-6"
                          :class="
                            header.column.getCanSort()
                              ? 'cursor-pointer select-none'
                              : ''
                          "
                          @click="
                            header.column.getToggleSortingHandler()?.(
                              $event,
                            )
                          "
                        >
                          <FlexRender
                            v-if="!header.isPlaceholder"
                            :render="header.column.columnDef.header"
                            :props="header.getContext()"
                          />
                          {{
                            { asc: " ↑", desc: "↓" }[
                              header.column.getIsSorted()
                            ]
                          }}
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                      <tr
                        v-for="row in table.getRowModel().rows"
                        :key="row.id"
                      >
                        <td
                          v-for="cell in row.getVisibleCells()"
                          :key="cell.id"
                          class="py-4 pl-4 pr-3 text-sm font-medium whitespace-nowrap text-stone-900 sm:pl-6"
                        >
                          <FlexRender
                            :render="cell.column.columnDef.cell"
                            :props="cell.getContext()"
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="border-t">
                    <div
                      class="flex items-center justify-between gap-2 p-2"
                    >
                      <div class="">
                        <button
                          class="w-16 p-1 mx-1 border rounded"
                          @click="() => table.setPageIndex(0)"
                          :disabled="!table.getCanPreviousPage()"
                        >
                          First
                        </button>
                        <button
                          class="w-16 p-1 mx-1 border rounded"
                          @click="() => table.previousPage()"
                          :disabled="!table.getCanPreviousPage()"
                        >
                          Previous
                        </button>
                        <button
                          class="w-16 p-1 mx-1 border rounded"
                          @click="() => table.nextPage()"
                          :disabled="!table.getCanNextPage()"
                        >
                          Next
                        </button>
                        <button
                          class="w-16 p-1 mx-1 border rounded"
                          @click="
                            () =>
                              table.setPageIndex(
                                table.getPageCount() - 1,
                              )
                          "
                          :disabled="!table.getCanNextPage()"
                        >
                          Last
                        </button>
                      </div>
                      <div class="flex">
                        <span class="flex items-center gap-1">
                          <div>Page</div>
                          <strong>
                            {{
                              table.getState().pagination.pageIndex +
                              1
                            }}
                            of
                            {{ table.getPageCount() }}
                          </strong>
                        </span>
                        <span class="flex items-center gap-1 ml-1">
                          | Go to page:
                          <input
                            type="number"
                            :value="goToPageNumber"
                            @change="handleGoToPage"
                            class="w-16 p-1 border rounded"
                          />
                        </span>
                      </div>
                      <div>
                        <select
                          :value="
                            table.getState().pagination.pageSize
                          "
                          @change="handlePageSizeChange"
                          class="text-sm border rounded"
                        >
                          <option
                            :key="pageSize"
                            :value="pageSize"
                            v-for="pageSize in pageSizes"
                          >
                            Show {{ pageSize }}
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { PlusIcon, ArrowPathIcon } from "@heroicons/vue/24/outline";
import { useCacheFirstQuery } from "src/composables/use-cache-first-query";
import gql from "graphql-tag";
import { computed, h, ref } from "vue";
import EditButton from "../components/EditButton.vue";
import LoadingOverlay from "src/components/LoadingOverlay.vue";
import ErrorState from "src/components/ErrorState.vue";
import { date } from "quasar";

import {
  FlexRender,
  getCoreRowModel,
  useVueTable,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  createColumnHelper,
} from "@tanstack/vue-table";

const MEDICAL_QUERY = gql`
  query listMedical($id: ID!) {
    medicals(organisationId: $id) {
      id
      status
      name
      medicalExpiryDate
      employment {
        companyName
      }
      patient {
        id
        fullName
        identificationNumber
        dob
      }
    }
  }
`;

function calculateAge(dob) {
  const today = new Date();
  const birthDate = new Date(dob);
  return date.getDateDiff(today, birthDate, "years");
}

const ageFilter = (row) => {
  const age = calculateAge(row.original.patient.dob);
  return age >= 55;
};

const { result, loading, isRefreshing, error, refetch } =
  useCacheFirstQuery(MEDICAL_QUERY, { id: 1 });

const medicals = computed(() => {
  return result.value?.medicals || [];
});

const medicalCount = computed(() => {
  return result.value?.medicals?.length || 0;
});

const pageSizes = [10, 20, 30, 40, 50];
const INITIAL_PAGE_INDEX = 0;
const goToPageNumber = ref(INITIAL_PAGE_INDEX + 1);

const sorting = ref([]);
const filter = ref("");

const showOver55 = ref(false);
const columnFilters = ref([]);

const columnHelper = createColumnHelper();

const columns = [
  columnHelper.accessor("patient.fullName", {
    header: () => "Full Name",
  }),

  columnHelper.accessor("id", {
    header: "Medical Ref.",
  }),
  columnHelper.accessor("patient.dob", {
    header: "dob",
  }),
  columnHelper.accessor("age", {
    header: () => "age",
    cell: ({ row }) =>
      h("div", [h("strong", calculateAge(row.original.patient.dob))]),
    filterFn: ageFilter,
  }),
  columnHelper.accessor("patient.identificationNumber", {
    header: "ID Number",
  }),
  columnHelper.accessor("employment.company.name", {
    header: "Company",
  }),

  columnHelper.accessor("medicalExpiryDate", {
    header: "Medical Expiry",
  }),

  columnHelper.accessor("status", {
    header: "status",
  }),

  columnHelper.accessor("edit", {
    header: "",
    cell: ({ row }) =>
      h(EditButton, {
        id: row.original.id,
        edit_path: "show_medical",
      }),
    enableSorting: false,
  }),
];

const table = useVueTable({
  data: medicals,
  columns,
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getCoreRowModel: getCoreRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  state: {
    get sorting() {
      return sorting.value;
    },
    get globalFilter() {
      return filter.value;
    },
    get columnFilters() {
      return columnFilters.value;
    },
  },
  onColumnFiltersChange: (updater) => {
    columnFilters.value =
      typeof updater === "function"
        ? updater(columnFilters.value)
        : updater;
  },
  onSortingChange: (updaterOrValue) => {
    sorting.value =
      typeof updaterOrValue === "function"
        ? updaterOrValue(sorting.value)
        : updaterOrValue;
  },
});

const toggleAgeFilter = () => {
  if (showOver55.value) {
    columnFilters.value = [{ id: "age", value: true }];
  } else {
    columnFilters.value = [];
  }
};

function handleGoToPage(e) {
  const page = e.target.value ? Number(e.target.value) - 1 : 0;
  goToPageNumber.value = page + 1;
  table.setPageIndex(page);
}

function handlePageSizeChange(e) {
  table.setPageSize(Number(e.target.value));
}
</script>
