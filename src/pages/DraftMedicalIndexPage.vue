<template>
  <q-page padding>
    <div class="flex content-center justify-center h-full">
      <div v-if="error">Error...</div>
      <div v-if="loading">Loading...</div>
      <div
        v-if="result && result.draftMedicals && medicalCount == 0"
        class="w-full"
      >
        <EmptyMedicalState />
      </div>
      <div
        v-else-if="result && result.draftMedicals && medicalCount > 0"
        class="w-full h-full"
      >
        <PageHeading class="pb-6">
          <template #heading>
            <div class="flex items-center">
              <span> Medicals </span>
              <router-link
                class="ml-2 bg-stone-500 px-2 py-1 rounded-md text-white uppercase text-sm"
                to="medicals"
              >
                All
              </router-link>
              <span
                class="ml-2 bg-stone-700 px-2 py-1 rounded-md text-white uppercase text-sm"
                >draft
              </span>
              <router-link
                class="ml-2 bg-stone-500 px-2 py-1 rounded-md text-white uppercase text-sm"
                to="signed_off_medicals"
                >Signed Off
              </router-link>
            </div>
          </template>

          <template #description>
            {{ medicalCount }} medical records
          </template>
          <template #buttons>
            <q-btn
              flat
              class="text-white bg-teal-700 text-bold"
              padding="md"
              no-caps
              to="/medicals/new"
              type="submit"
            >
              <template v-slot:loading>
                <q-spinner-ios />
              </template>
              <PlusIcon class="w-5 h-5 mr-1 text-bold" />
              <div class="text-sm">Add Medical</div>
            </q-btn>
          </template>
        </PageHeading>

        <div class="px-4 sm:px-6 lg:px-8">
          <div class="flex flex-col mt-8">
            <div
              class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
            >
              <div
                class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
              >
                <div
                  class="overflow-hidden shadow ring-0.5 ring-stone-50 ring-opacity-5 md:rounded-sm"
                >
                  <table class="min-w-full divide-y divide-gray-100">
                    <thead class="">
                      <tr>
                        <th
                          scope="col"
                          class="py-3 pl-4 pr-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase sm:pl-6"
                        >
                          Patient full name
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Medical Reference
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          ID/Passport Ref
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Company
                        </th>

                        <th
                          scope="col"
                          class="vk-hidden xl:block px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Expiry
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          class="relative py-3 pl-3 pr-4 sm:pr-6"
                        >
                          <span class="sr-only">Edit</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                      <tr
                        v-for="medical in result.draftMedicals"
                        :key="medical.id"
                      >
                        <td
                          class="py-4 pl-4 pr-3 text-sm font-medium whitespace-nowrap text-stone-900 sm:pl-6"
                        >
                          {{ medical.patient.fullName }}
                        </td>
                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          OMA-{{ medical.name }}
                        </td>
                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          {{ medical.patient.identificationNumber }}
                        </td>
                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          {{ medical.employment.company.name }}
                        </td>

                        <td
                          class="px-3 py-4 text-sm vk-hidden xl:block whitespace-nowrap text-stone-500"
                        >
                          {{ medical.medicalExpiryDate }}
                        </td>

                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          {{
                            medical.status == "signed_off"
                              ? "Signed Off"
                              : "Draft"
                          }}
                        </td>
                        <td
                          class="relative flex py-4 pl-3 pr-4 text-sm font-medium text-left whitespace-nowrap sm:pr-6"
                        >
                          <router-link
                            :to="{
                              name: 'show_medical',
                              params: { id: medical.id },
                            }"
                          >
                            <ArrowRightIcon
                              class="w-5 h-5 text-stone-400 hover:text-teal-900"
                            />
                          </router-link>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { PlusIcon, ArrowRightIcon } from "@heroicons/vue/24/outline";
import { useQuery } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed } from "vue";

let medicalCount = computed(() => {
  return result.value.draftMedicals.length;
});

const { result, loading, error } = useQuery(
  gql`
    query listMedical($id: ID!) {
      draftMedicals(organisationId: $id) {
        id
        status
        name
        medicalExpiryDate
        employment {
          company {
            name
          }
        }
        patient {
          id
          fullName
          identificationNumber
        }
      }
    }
  `,
  {
    id: 1,
  },
  {
    fetchPolicy: "cache-and-network",
  },
);
</script>
