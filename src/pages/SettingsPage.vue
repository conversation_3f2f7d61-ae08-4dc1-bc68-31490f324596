<template>
  <q-page padding>
    <div>
      <div class="">
        <div class="flex flex-col max-w-4xl">
          <main class="flex-1">
            <div class="relative max-w-4xl md:px-8 xl:px-0">
              <div class="">
                <PageHeading class="pb-6">
                  <template #heading> Settings </template>

                  <template #buttons>
                    <button
                      class="px-4 py-2 text-stone-600 bg-stone-50 text-bold hover:bg-stone-100"
                      @click="signOut"
                    >
                      <div class="text-sm">Sign out</div>
                    </button>
                  </template>
                </PageHeading>

                <div class="px-4 sm:px-6 md:px-0">
                  <div class="py-6">
                    <!-- Tabs -->
                    <div class="md:hidden">
                      <label for="selected-tab" class="sr-only"
                        >Select a tab</label
                      >
                      <select
                        id="selected-tab"
                        name="selected-tab"
                        class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-stone-500 focus:outline-none focus:ring-stone-500 sm:text-sm"
                      >
                        <option
                          v-for="tab in tabs"
                          :key="tab.name"
                          :selected="tab.current"
                        >
                          {{ tab.name }}
                        </option>
                      </select>
                    </div>

                    <TabGroup>
                      <TabList
                        class="flex mb-px space-x-8 vk-hidden md:block"
                      >
                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Profile
                          </button>
                        </Tab>

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Password
                          </button></Tab
                        >
                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Signature
                          </button></Tab
                        >

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Organisation
                          </button></Tab
                        >

                        <Tab as="template" v-slot="{ selected }">
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Clinic
                          </button></Tab
                        >

                        <!-- Cache Tab (Development Only) -->
                        <Tab
                          v-if="isDevelopment"
                          as="template"
                          v-slot="{ selected }"
                        >
                          <button
                            :class="{
                              'border-stone-500 text-stone-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                selected,
                              'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm':
                                !selected,
                            }"
                          >
                            Cache
                          </button></Tab
                        >
                      </TabList>
                      <TabPanels>
                        <TabPanel>Content 1</TabPanel>
                        <TabPanel>Content 2</TabPanel>
                        <TabPanel>Content 3</TabPanel>
                        <TabPanel>Content 4</TabPanel>
                        <TabPanel><ClinicTabPanel /></TabPanel>

                        <!-- Cache Tab Panel (Development Only) -->
                        <TabPanel v-if="isDevelopment">
                          <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                              <h3
                                class="text-lg leading-6 font-medium text-gray-900"
                              >
                                Cache Diagnostics
                              </h3>
                              <div
                                class="mt-2 max-w-xl text-sm text-gray-500"
                              >
                                <p>
                                  Monitor and manage the Apollo
                                  GraphQL cache.
                                </p>
                              </div>

                              <!-- Cache Statistics -->
                              <div
                                v-if="cacheStats"
                                class="mt-5"
                                data-testid="cache-stats"
                              >
                                <div
                                  class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"
                                >
                                  <div
                                    class="bg-gray-50 overflow-hidden shadow rounded-lg"
                                  >
                                    <div class="p-5">
                                      <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                          <div
                                            class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"
                                          >
                                            <span
                                              class="text-white text-sm font-bold"
                                              >E</span
                                            >
                                          </div>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                          <dl>
                                            <dt
                                              class="text-sm font-medium text-gray-500 truncate"
                                            >
                                              Entities
                                            </dt>
                                            <dd
                                              class="text-lg font-medium text-gray-900"
                                            >
                                              {{
                                                cacheStats.entityCount
                                              }}
                                            </dd>
                                          </dl>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div
                                    class="bg-gray-50 overflow-hidden shadow rounded-lg"
                                  >
                                    <div class="p-5">
                                      <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                          <div
                                            class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"
                                          >
                                            <span
                                              class="text-white text-sm font-bold"
                                              >S</span
                                            >
                                          </div>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                          <dl>
                                            <dt
                                              class="text-sm font-medium text-gray-500 truncate"
                                            >
                                              Size
                                            </dt>
                                            <dd
                                              class="text-lg font-medium text-gray-900"
                                            >
                                              {{
                                                cacheStats.cacheSizeKB
                                              }}KB
                                            </dd>
                                          </dl>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div
                                    class="bg-gray-50 overflow-hidden shadow rounded-lg"
                                  >
                                    <div class="p-5">
                                      <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                          <div
                                            class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"
                                          >
                                            <span
                                              class="text-white text-sm font-bold"
                                              >Q</span
                                            >
                                          </div>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                          <dl>
                                            <dt
                                              class="text-sm font-medium text-gray-500 truncate"
                                            >
                                              Queries
                                            </dt>
                                            <dd
                                              class="text-lg font-medium text-gray-900"
                                            >
                                              {{
                                                cacheStats.queryCount
                                              }}
                                            </dd>
                                          </dl>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div
                                    class="bg-gray-50 overflow-hidden shadow rounded-lg"
                                  >
                                    <div class="p-5">
                                      <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                          <div
                                            class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"
                                          >
                                            <span
                                              class="text-white text-sm font-bold"
                                              >P</span
                                            >
                                          </div>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                          <dl>
                                            <dt
                                              class="text-sm font-medium text-gray-500 truncate"
                                            >
                                              Persisted
                                            </dt>
                                            <dd
                                              class="text-lg font-medium text-gray-900"
                                            >
                                              {{
                                                cacheStats.persistedSizeKB
                                              }}KB
                                            </dd>
                                          </dl>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Entity Types Breakdown -->
                                <div
                                  v-if="cacheStats.entityTypes"
                                  class="mt-5"
                                >
                                  <h4
                                    class="text-sm font-medium text-gray-900 mb-3"
                                  >
                                    Entity Types
                                  </h4>
                                  <div
                                    class="bg-gray-50 rounded-lg p-4"
                                  >
                                    <div
                                      class="grid grid-cols-2 gap-4 text-sm"
                                    >
                                      <div
                                        v-for="(
                                          count, type
                                        ) in cacheStats.entityTypes"
                                        :key="type"
                                        class="flex justify-between"
                                      >
                                        <span class="text-gray-600"
                                          >{{ type }}:</span
                                        >
                                        <span
                                          class="font-medium text-gray-900"
                                          >{{ count }}</span
                                        >
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Last Updated -->
                                <div
                                  class="mt-3 text-xs text-gray-500"
                                >
                                  Last updated:
                                  {{
                                    formatTimestamp(
                                      cacheStats.timestamp,
                                    )
                                  }}
                                </div>
                              </div>

                              <!-- Action Buttons -->
                              <div class="flex flex-wrap gap-3 mt-5">
                                <button
                                  @click="refreshCacheStats"
                                  :disabled="loadingStats"
                                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 disabled:opacity-50"
                                >
                                  <ArrowPathIcon
                                    class="w-4 h-4 mr-2 -ml-1"
                                    :class="{
                                      'animate-spin': loadingStats,
                                    }"
                                  />
                                  Refresh Stats
                                </button>

                                <button
                                  @click="validatePersistence"
                                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
                                >
                                  Test Persistence
                                </button>

                                <button
                                  @click="toggleOfflineMode"
                                  :class="[
                                    'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
                                    isOfflineMode
                                      ? 'border-orange-300 text-orange-800 bg-orange-50 hover:bg-orange-100 focus:ring-orange-500'
                                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-stone-500',
                                  ]"
                                >
                                  <WifiIcon
                                    v-if="!isOfflineMode"
                                    class="w-4 h-4 mr-2 -ml-1"
                                  />
                                  <ExclamationTriangleIcon
                                    v-else
                                    class="w-4 h-4 mr-2 -ml-1"
                                  />
                                  {{
                                    isOfflineMode
                                      ? "Go Online"
                                      : "Go Offline"
                                  }}
                                </button>

                                <button
                                  @click="clearCache"
                                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                  <TrashIcon
                                    class="w-4 h-4 mr-2 -ml-1"
                                  />
                                  Clear Cache
                                </button>
                              </div>

                              <!-- Offline Mode Status -->
                              <div
                                v-if="isOfflineMode"
                                class="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md"
                              >
                                <div class="flex">
                                  <ExclamationTriangleIcon
                                    class="h-5 w-5 text-orange-400"
                                  />
                                  <div class="ml-3">
                                    <h3
                                      class="text-sm font-medium text-orange-800"
                                    >
                                      Offline Mode Active
                                    </h3>
                                    <div
                                      class="mt-1 text-sm text-orange-700"
                                    >
                                      <p>
                                        Network requests are being
                                        blocked to simulate offline
                                        conditions.
                                        <span
                                          v-if="
                                            formattedOfflineDuration
                                          "
                                        >
                                          Offline for
                                          {{
                                            formattedOfflineDuration
                                          }}.
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Status Messages -->
                              <div
                                v-if="cacheMessage"
                                class="mt-4 p-3 rounded-md"
                                :class="{
                                  'bg-green-50 text-green-800':
                                    cacheMessageType === 'success',
                                  'bg-amber-50 text-amber-800':
                                    cacheMessageType === 'info',
                                  'bg-orange-50 text-orange-800':
                                    cacheMessageType === 'warning',
                                  'bg-red-50 text-red-800':
                                    cacheMessageType === 'error',
                                }"
                              >
                                {{ cacheMessage }}
                              </div>
                            </div>
                          </div>
                        </TabPanel>
                      </TabPanels>
                    </TabGroup>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRouter } from "vue-router";
import { useQuasar } from "quasar";
import { ref, onMounted, computed } from "vue";
import {
  getCacheStats,
  clearCache as clearCacheUtil,
  validateCachePersistence,
} from "src/utils/apollo-cache-debug";
import {
  ArrowPathIcon,
  TrashIcon,
  WifiIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/outline";
import { useApolloClient } from "@vue/apollo-composable";
import { useOfflineMode } from "src/composables/use-offline-mode";
import {
  TabGroup,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from "@headlessui/vue";

const router = useRouter();
const $q = useQuasar();

// Cache diagnostics state
const isDevelopment = computed(() => process.env.DEV);
const cacheStats = ref(null);
const loadingStats = ref(false);
const cacheMessage = ref("");
const cacheMessageType = ref("success");

const { client } = useApolloClient();

// Offline mode functionality
const {
  isOfflineMode,
  formattedOfflineDuration,
  toggleOfflineMode: toggleOffline,
} = useOfflineMode();

// Wrapper for offline toggle with user feedback
const toggleOfflineMode = () => {
  toggleOffline();

  // Show feedback message
  if (isOfflineMode.value) {
    cacheMessage.value =
      "Offline mode activated - network requests will be blocked";
    cacheMessageType.value = "warning";
  } else {
    cacheMessage.value =
      "Online mode restored - network requests will work normally";
    cacheMessageType.value = "success";
  }

  // Clear message after 3 seconds
  setTimeout(() => {
    cacheMessage.value = "";
  }, 3000);
};

// Format timestamp for display
const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};

// Refresh cache statistics
const refreshCacheStats = async () => {
  loadingStats.value = true;
  cacheMessage.value = "";

  try {
    const stats = await getCacheStats(client.cache);
    cacheStats.value = stats;

    if (stats.error) {
      cacheMessage.value = `Error getting cache stats: ${stats.error}`;
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error("[Settings] Failed to get cache stats:", error);
    cacheMessage.value = `Failed to get cache stats: ${error.message}`;
    cacheMessageType.value = "error";
  } finally {
    loadingStats.value = false;
  }
};

// Validate cache persistence
const validatePersistence = async () => {
  cacheMessage.value = "";

  try {
    const result = await validateCachePersistence();

    if (result.success) {
      cacheMessage.value = `${result.message} (${result.latency}ms)`;
      cacheMessageType.value = "success";
    } else {
      cacheMessage.value = result.message;
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error(
      "[Settings] Failed to validate persistence:",
      error,
    );
    cacheMessage.value = `Persistence validation failed: ${error.message}`;
    cacheMessageType.value = "error";
  }
};

// Clear cache with confirmation
const clearCache = async () => {
  if (
    !confirm(
      "Are you sure you want to clear the cache? This will remove all cached data and require fresh data loading.",
    )
  ) {
    return;
  }

  cacheMessage.value = "";

  try {
    const success = await clearCacheUtil(client.cache);

    if (success) {
      cacheMessage.value = "Cache cleared successfully";
      cacheMessageType.value = "success";

      // Refresh stats to show empty cache
      await refreshCacheStats();
    } else {
      cacheMessage.value = "Failed to clear cache";
      cacheMessageType.value = "error";
    }
  } catch (error) {
    console.error("[Settings] Failed to clear cache:", error);
    cacheMessage.value = `Failed to clear cache: ${error.message}`;
    cacheMessageType.value = "error";
  }
};

// Load initial cache stats on mount (dev only)
onMounted(() => {
  if (isDevelopment.value) {
    refreshCacheStats();
  }
});

function signOut(e) {
  $q.cookies.remove("occusolve-token");
  router.push("/login");
}
</script>
