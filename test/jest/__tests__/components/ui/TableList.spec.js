import { describe, it, expect } from '@jest/globals'
import TableList from 'src/components/TableList.vue'
import { mountComponent } from 'test/jest/helpers/test-utils'

describe('TableList.vue', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mountComponent(TableList)
  })

  describe('Component Rendering', () => {
    it('renders the table component', () => {
      expect(wrapper.find('table').exists()).toBe(true)
    })

    it('displays the page heading', () => {
      expect(wrapper.text()).toContain('Patients')
      expect(wrapper.text()).toContain('24 Entries found')
    })

    it('renders table headers', () => {
      const headers = wrapper.findAll('th')
      expect(headers).toHaveLength(5) // Name, Title, Email, Role, Actions

      expect(headers[0].text()).toContain('Name')
      expect(headers[1].text()).toContain('Title')
      expect(headers[2].text()).toContain('Email')
      expect(headers[3].text()).toContain('Role')
    })

    it('displays add new entry button', () => {
      const addButton = wrapper.find('button')
      expect(addButton.exists()).toBe(true)
      expect(addButton.text()).toContain('Add new entry')
    })
  })

  describe('Table Data', () => {
    it('renders table rows with mock data', () => {
      const rows = wrapper.findAll('tbody tr')
      expect(rows.length).toBeGreaterThan(0)
    })

    it('displays person data in table cells', () => {
      const firstRow = wrapper.find('tbody tr')
      expect(firstRow.text()).toContain('Lindsay Walton')
      expect(firstRow.text()).toContain('Front-end Developer')
      expect(firstRow.text()).toContain('<EMAIL>')
      expect(firstRow.text()).toContain('Member')
    })

    it('includes edit links for each row', () => {
      const editLinks = wrapper.findAll('a')
      expect(editLinks.length).toBeGreaterThan(0)

      const firstEditLink = editLinks[0]
      expect(firstEditLink.text()).toContain('Edit')
      expect(firstEditLink.attributes('href')).toBe('#')
    })
  })

  describe('Navigation', () => {
    it('displays back navigation', () => {
      expect(wrapper.text()).toContain('Back')
    })

    it('handles navigation breadcrumb element', () => {
      const navigation = wrapper.find('[data-testid="navigation"]')
      // Navigation element is optional, so we test for its presence
      expect(navigation.exists()).toBeDefined()
    })
  })

  describe('Styling and Layout', () => {
    it('applies correct CSS classes for responsive design', () => {
      const table = wrapper.find('table')
      expect(table.classes()).toContain('min-w-full')
      expect(table.classes()).toContain('divide-y')
      expect(table.classes()).toContain('divide-gray-300')
    })

    it('has proper table structure', () => {
      expect(wrapper.find('thead').exists()).toBe(true)
      expect(wrapper.find('tbody').exists()).toBe(true)
    })

    it('applies hover effects to edit links', () => {
      const editLink = wrapper.find('a')
      expect(editLink.classes()).toContain('text-teal-600')
      expect(editLink.classes()).toContain('hover:text-teal-900')
    })
  })

  describe('Accessibility', () => {
    it('includes screen reader text for edit links', () => {
      const srText = wrapper.find('.sr-only')
      expect(srText.exists()).toBe(true)
    })

    it('has proper table scope attributes', () => {
      const headers = wrapper.findAll('th[scope="col"]')
      expect(headers.length).toBeGreaterThan(0)
    })

    it('includes proper heading structure', () => {
      // Check that PageHeading component is used properly
      expect(wrapper.findComponent({ name: 'PageHeading' }).exists()).toBe(true)
    })
  })

  describe('Component Props and Slots', () => {
    it('uses PageHeading component with slots', () => {
      const pageHeading = wrapper.findComponent({ name: 'PageHeading' })
      expect(pageHeading.exists()).toBe(true)
    })

    it('passes correct data to template', () => {
      expect(wrapper.vm.people).toBeDefined()
      expect(Array.isArray(wrapper.vm.people)).toBe(true)
      expect(wrapper.vm.people.length).toBeGreaterThan(0)
    })
  })

  describe('Responsive Design', () => {
    it('includes responsive classes for mobile', () => {
      const container = wrapper.find('.overflow-x-auto')
      expect(container.exists()).toBe(true)
    })

    it('has responsive padding classes', () => {
      const container = wrapper.find('.px-4.sm\\:px-6.lg\\:px-8')
      expect(container.exists()).toBe(true)
    })
  })
})
