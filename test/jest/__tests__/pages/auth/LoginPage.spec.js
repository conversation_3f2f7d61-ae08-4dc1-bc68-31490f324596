import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import LoginPage from 'src/pages/LoginPage.vue'
import { mountWithApollo } from 'test/jest/helpers/apollo-mock'
import { setInputValue, flushPromises, createMockQuasar } from 'test/jest/helpers/test-utils'

// Mock router
const mockPush = jest.fn()
jest.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

describe('LoginPage.vue', () => {
  let wrapper
  let mockQuasar



  beforeEach(() => {
    mockQuasar = createMockQuasar()
    mockPush.mockClear()
  })

  const createWrapper = (mocks = []) => {
    return mountWithApollo(LoginPage, {
      global: {
        mocks: {
          $q: mockQuasar
        }
      }
    }, mocks)
  }

  describe('Component Rendering', () => {
    it('renders the login form', async () => {
      wrapper = createWrapper()
      await flushPromises()

      expect(wrapper.find('form').exists()).toBe(true)
      expect(wrapper.find('input[type="email"]').exists()).toBe(true)
      expect(wrapper.find('input[type="password"]').exists()).toBe(true)
      expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
    })

    it('displays the login form title', () => {
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('Sign in')
    })

    it('shows login button with correct text', () => {
      wrapper = createWrapper()

      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.exists()).toBe(true)
      expect(submitButton.text()).toContain('Sign in')
    })
  })

  describe('Form Validation', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await flushPromises()
    })

    it('has form validation setup', async () => {
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')

      expect(emailInput.exists()).toBe(true)
      expect(passwordInput.exists()).toBe(true)

      // Test that form has validation (component should have vuelidate setup)
      expect(wrapper.vm.v$).toBeDefined()
    })

    it('allows submission with valid credentials', async () => {
      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')

      await setInputValue(emailInput, '<EMAIL>')
      await setInputValue(passwordInput, 'password123')

      // Check that form validation passes
      expect(wrapper.vm.v$.$invalid).toBe(false)
    })
  })

  describe('Authentication Flow', () => {
    it('has login mutation setup', async () => {
      wrapper = createWrapper()
      await flushPromises()

      // Test that component has the necessary methods for login
      expect(wrapper.vm.sendForm).toBeDefined()
      expect(wrapper.vm.formData).toBeDefined()
      expect(wrapper.vm.formData.email).toBeDefined()
      expect(wrapper.vm.formData.password).toBeDefined()
    })

    it('has form data binding', async () => {
      wrapper = createWrapper()
      await flushPromises()

      const emailInput = wrapper.find('input[type="email"]')
      const passwordInput = wrapper.find('input[type="password"]')

      await setInputValue(emailInput, '<EMAIL>')
      await setInputValue(passwordInput, 'password123')

      expect(wrapper.vm.formData.email).toBe('<EMAIL>')
      expect(wrapper.vm.formData.password).toBe('password123')
    })
  })

  describe('Loading States', () => {
    it('has loading state management', async () => {
      wrapper = createWrapper()
      await flushPromises()

      // Test that component has loading state property
      expect(wrapper.vm.sendFormLoading).toBeDefined()
      expect(typeof wrapper.vm.sendFormLoading).toBe('boolean')
    })
  })

  describe('Form Data Binding', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await flushPromises()
    })

    it('updates form data when email input changes', async () => {
      const emailInput = wrapper.find('input[type="email"]')
      await setInputValue(emailInput, '<EMAIL>')

      expect(wrapper.vm.formData.email).toBe('<EMAIL>')
    })

    it('updates form data when password input changes', async () => {
      const passwordInput = wrapper.find('input[type="password"]')
      await setInputValue(passwordInput, 'password123')

      expect(wrapper.vm.formData.password).toBe('password123')
    })
  })
})
