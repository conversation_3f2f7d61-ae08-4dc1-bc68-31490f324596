{"name": "occumed-cross", "version": "0.0.1", "description": "A Quasar Project", "productName": "OccuSolve", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,css,html,md,json}\" --ignore-path .gitignore", "test": "echo \"See package.json => scripts for available tests.\" && exit 0", "test:unit": "npx jest --updateSnapshot", "test:unit:ci": "npx jest --ci", "test:unit:coverage": "npx jest --coverage", "test:unit:watch": "npx jest --watch", "test:unit:watchAll": "npx jest --watchAll", "serve:test:coverage": "quasar serve test/jest/coverage/lcov-report/ --port 8788", "concurrently:dev:jest": "concurrently \"quasar dev\" \"jest --watch\"", "test:e2e": "cross-env NODE_ENV=test start-test \"quasar dev\" http-get://localhost:9002 \"cypress open\"", "test:e2e:ci": "cross-env NODE_ENV=test start-test \"quasar dev\" http-get://localhost:9002 \"cypress run\"", "test:component": "cross-env NODE_ENV=test cypress run-ct", "test:component:ci": "cross-env NODE_ENV=test cypress run-ct", "test:unit:ui": "majestic", "test:all": "./scripts/test-all.sh", "test:e2e:headless": "npx cypress run --headless", "test:watch": "./scripts/test-watch.sh", "test:coverage": "./scripts/coverage-report.sh", "test:coverage:open": "./scripts/coverage-report.sh --open", "test:ci": "npm run lint && npm run test:unit:coverage", "test:pre-commit": "npm run lint && npm run test:unit"}, "dependencies": {"@apollo/client": "^3.6.9", "@headlessui/vue": "^1.6.5", "@heroicons/vue": "^2.0.0", "@popperjs/core": "^2.11.8", "@quasar/babel-preset-app": "^2.0.1", "@quasar/extras": "^1.0.0", "@tanstack/vue-table": "^8.20.5", "@vue/apollo-composable": "^4.0.0-alpha.18", "@vue/apollo-util": "^4.0.0-alpha.18", "@vuelidate/core": "^2.0.0-alpha.43", "@vuelidate/validators": "^2.0.0-alpha.31", "@vueuse/core": "^9.0.2", "apollo-link-queue": "^3.1.0", "apollo-link-serialize": "^4.0.0", "apollo-upload-client": "^17.0.0", "apollo3-cache-persist": "^0.14.1", "filepond": "^4.30.4", "filepond-plugin-file-validate-type": "^1.2.8", "graphql": "^16.5.0", "graphql-tag": "^2.12.6", "localforage": "^1.10.0", "maska": "^1.5.0", "quasar": "^2.6.0", "v-calendar": "^3.0.0-alpha.8", "vue": "^3.0.0", "vue-filepond": "^7.0.3", "vue-router": "^4.0.0", "vue-the-mask": "^0.11.1"}, "devDependencies": {"@faker-js/faker": "^7.3.0", "@miragejs/graphql": "^0.1.13", "@quasar/app-vite": "^2.3.0", "@quasar/quasar-app-extension-testing": "^2.0.4", "@quasar/quasar-app-extension-testing-e2e-cypress": "^6.2.1", "@quasar/quasar-app-extension-testing-unit-jest": "^2.2.5", "@tailwindcss/forms": "^0.5.2", "@vue/vue3-jest": "^29.2.6", "autoprefixer": "^10.4.2", "babel-jest": "^30.0.5", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-cypress": "^2.11.3", "eslint-plugin-jest": "^28.14.0", "eslint-plugin-vue": "^8.5.0", "identity-obj-proxy": "^3.0.0", "jest-serializer-vue": "^3.1.0", "jest-transform-stub": "^2.0.0", "majestic": "^1.7.0", "miragejs": "^0.1.45", "prettier": "^2.4.1", "tailwindcss": "^3.1.4", "unplugin-vue-components": "^29.0.0", "vite-plugin-simple-gql": "^0.5.0"}, "engines": {"node": "^18 || ^19 || ^20", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}